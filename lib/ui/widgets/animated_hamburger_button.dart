import 'package:flutter/material.dart';

/// An animated button that transitions between hamburger (three lines) and close (X) icons
class AnimatedHamburgerButton extends StatefulWidget {
  /// Whether the button should show the close (X) state
  final bool isOpen;
  
  /// Callback when the button is pressed
  final VoidCallback onPressed;
  
  /// Color of the icon
  final Color? color;
  
  /// Size of the icon
  final double size;
  
  /// Duration of the animation
  final Duration animationDuration;

  /// Constructor
  const AnimatedHamburgerButton({
    super.key,
    required this.isOpen,
    required this.onPressed,
    this.color,
    this.size = 24.0,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  State<AnimatedHamburgerButton> createState() => _AnimatedHamburgerButtonState();
}

class _AnimatedHamburgerButtonState extends State<AnimatedHamburgerButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    // Set initial state
    if (widget.isOpen) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(AnimatedHamburgerButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isOpen != oldWidget.isOpen) {
      if (widget.isOpen) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: widget.onPressed,
      icon: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return CustomPaint(
            size: Size(widget.size, widget.size),
            painter: _HamburgerIconPainter(
              progress: _animation.value,
              color: widget.color ?? Theme.of(context).iconTheme.color ?? Colors.black,
            ),
          );
        },
      ),
    );
  }
}

/// Custom painter for the animated hamburger/close icon
class _HamburgerIconPainter extends CustomPainter {
  final double progress;
  final Color color;

  _HamburgerIconPainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round;

    final width = size.width;
    final height = size.height;
    final centerX = width / 2;
    final centerY = height / 2;

    if (progress == 0.0) {
      // Draw hamburger menu (three horizontal lines)
      final lineLength = width * 0.6;
      final lineStart = (width - lineLength) / 2;

      // Top line
      canvas.drawLine(
        Offset(lineStart, centerY - 6),
        Offset(lineStart + lineLength, centerY - 6),
        paint,
      );

      // Middle line
      canvas.drawLine(
        Offset(lineStart, centerY),
        Offset(lineStart + lineLength, centerY),
        paint,
      );

      // Bottom line
      canvas.drawLine(
        Offset(lineStart, centerY + 6),
        Offset(lineStart + lineLength, centerY + 6),
        paint,
      );
    } else if (progress == 1.0) {
      // Draw X (two diagonal lines)
      final lineLength = width * 0.5;
      final halfLength = lineLength / 2;

      // Top-left to bottom-right diagonal
      canvas.drawLine(
        Offset(centerX - halfLength, centerY - halfLength),
        Offset(centerX + halfLength, centerY + halfLength),
        paint,
      );

      // Top-right to bottom-left diagonal
      canvas.drawLine(
        Offset(centerX + halfLength, centerY - halfLength),
        Offset(centerX - halfLength, centerY + halfLength),
        paint,
      );
    } else {
      // Animate between hamburger and X
      final lineLength = width * 0.6;
      final lineStart = (width - lineLength) / 2;

      // Calculate positions for animation
      final topLineY = centerY - 6;
      final bottomLineY = centerY + 6;

      // Animate top line: from horizontal to diagonal
      final topStartX = lineStart + (centerX - lineStart - lineLength/2) * progress;
      final topStartY = topLineY + (centerY - lineLength/2 - topLineY) * progress;
      final topEndX = lineStart + lineLength + (centerX + lineLength/2 - lineStart - lineLength) * progress;
      final topEndY = topLineY + (centerY + lineLength/2 - topLineY) * progress;

      canvas.drawLine(
        Offset(topStartX, topStartY),
        Offset(topEndX, topEndY),
        paint,
      );

      // Middle line: fade out
      paint.color = color.withValues(alpha: 1.0 - progress);
      canvas.drawLine(
        Offset(lineStart, centerY),
        Offset(lineStart + lineLength, centerY),
        paint,
      );
      paint.color = color; // Reset color

      // Animate bottom line: from horizontal to diagonal
      final bottomStartX = lineStart + (centerX + lineLength/2 - lineStart) * progress;
      final bottomStartY = bottomLineY + (centerY - lineLength/2 - bottomLineY) * progress;
      final bottomEndX = lineStart + lineLength + (centerX - lineLength/2 - lineStart - lineLength) * progress;
      final bottomEndY = bottomLineY + (centerY + lineLength/2 - bottomLineY) * progress;

      canvas.drawLine(
        Offset(bottomStartX, bottomStartY),
        Offset(bottomEndX, bottomEndY),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant _HamburgerIconPainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.color != color;
  }
}
