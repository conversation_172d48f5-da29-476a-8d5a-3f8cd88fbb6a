import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:noeji/exceptions/limit_exceptions.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/services/audio/audio_providers.dart';
import 'package:noeji/services/audio/chat_recording_controller.dart';
import 'package:noeji/services/chat/chat_rate_limit_service_provider.dart';
import 'package:noeji/services/firebase/firebase_providers.dart';
import 'package:noeji/services/limits/app_limits_providers.dart';
import 'package:noeji/services/preferences/app_behavior_provider.dart';
import 'package:noeji/utils/error_utils.dart';
import 'package:noeji/services/tour/tour_service.dart';
import 'package:noeji/ui/providers/audio_providers.dart';
import 'package:noeji/ui/providers/chat_provider.dart';
import 'package:noeji/ui/providers/idea_provider.dart';
import 'package:noeji/ui/providers/ideabook_detail_tab_provider.dart';
import 'package:noeji/ui/providers/note_provider.dart';
import 'package:noeji/ui/providers/suggested_prompts_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/common/reusable_audio_recording_panel.dart';
import 'package:noeji/ui/widgets/paywall_handler.dart';
import 'package:noeji/ui/widgets/showcase_tour.dart';
import 'package:noeji/utils/logger.dart';

/// Widget for displaying the Chat tab in the ideabook detail screen
class ChatTab extends ConsumerStatefulWidget {
  /// The ideabook to display chat for
  final Ideabook ideabook;

  /// Constructor
  const ChatTab({super.key, required this.ideabook});

  @override
  ConsumerState<ChatTab> createState() => _ChatTabState();
}

class _ChatTabState extends ConsumerState<ChatTab> {
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ScrollController _inputScrollController =
      ScrollController(); // For input field scrolling
  final FocusNode _focusNode = FocusNode();

  // State to track if text field is empty
  bool _isTextEmpty = true;

  // State to track if text exceeds character limit
  bool _isOverCharLimit = false;

  // Character limit constant
  static const int _characterLimit = 200;

  // Track the number of messages to detect when new ones are added
  int _previousMessageCount = 0;

  // Cached max notes per ideabook limit
  int? _maxNotesPerIdeabook;

  @override
  void initState() {
    super.initState();

    // Initialize the max notes per ideabook limit
    _initializeMaxNotesPerIdeabook();

    // Add listener to text controller to track when text changes
    _textController.addListener(_onTextChanged);

    // Check rate limits when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Trigger a rate limit check when the widget initializes
      ref.read(checkChatRateLimitProvider.future);
    });

    // Set up focus node with key event handler for Shift+Enter
    _focusNode.onKeyEvent = (node, event) {
      if (event is KeyDownEvent &&
          event.logicalKey == LogicalKeyboardKey.enter &&
          HardwareKeyboard.instance.isShiftPressed &&
          !_isTextEmpty) {
        // Check if chat is loading
        final chatState = ref.read(chatNotifierProvider(widget.ideabook.id));
        if (chatState.isLoading) {
          return KeyEventResult.handled; // Don't send if already loading
        }

        // Check if message exceeds character limit
        if (_textController.text.length > _characterLimit) {
          // Show error message via chat state
          ref
              .read(chatNotifierProvider(widget.ideabook.id).notifier)
              .setError(
                'Message exceeds $_characterLimit character limit. Please shorten your message.',
              );
          return KeyEventResult.handled;
        }

        // Check rate limit before sending
        final rateLimitResult = ref.read(chatRateLimitStatusProvider);
        if (rateLimitResult != null && rateLimitResult.isLimitReached) {
          // Handle rate limit with paywall logic
          _handleRateLimit(rateLimitResult);
          return KeyEventResult.handled;
        }

        // Get the current ideas async state
        final ideasAsync = ref.read(ideabookIdeasProvider(widget.ideabook.id));
        // Send the message
        _sendMessage(ideasAsync);
        return KeyEventResult.handled;
      }
      return KeyEventResult.ignored;
    };

    // Initialize the previous message count and scroll to bottom on first load
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final chatState = ref.read(chatNotifierProvider(widget.ideabook.id));
      _previousMessageCount = chatState.messages.length;

      // If there are messages, scroll to bottom on initial load
      if (chatState.messages.isNotEmpty) {
        // Directly position at the bottom without any delay or animation
        if (_scrollController.hasClients) {
          _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
        } else {
          // If controller doesn't have clients yet, wait for the next frame
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (_scrollController.hasClients) {
              _scrollController.jumpTo(
                _scrollController.position.maxScrollExtent,
              );
            }
          });
        }
      }
    });

    // Listen for transcription results
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listenManual(chatTranscriptionResultProvider, (previous, next) {
        if (next != null) {
          // Check the app behavior setting to determine what to do with the transcription
          final appBehavior = ref.read(appBehaviorProvider);
          final shouldSendOnFinish = appBehavior.sendVoiceChatOnFinish;

          if (shouldSendOnFinish) {
            // Set the transcribed text in the text field and send it immediately
            final currentText = _textController.text;
            final spacer = currentText.isEmpty ? '' : ' ';
            _textController.text = '$currentText$spacer$next';

            // Move cursor to the end
            _textController.selection = TextSelection.fromPosition(
              TextPosition(offset: _textController.text.length),
            );

            // Send the message immediately
            final ideasAsync = ref.read(
              ideabookIdeasProvider(widget.ideabook.id),
            );
            _sendMessage(ideasAsync);
          } else {
            // Default behavior: Add the transcribed text to the text field for editing
            final currentText = _textController.text;
            final spacer = currentText.isEmpty ? '' : ' ';
            _textController.text = '$currentText$spacer$next';

            // Move cursor to the end
            _textController.selection = TextSelection.fromPosition(
              TextPosition(offset: _textController.text.length),
            );
          }

          // Reset the transcription result
          ref.read(chatTranscriptionResultProvider.notifier).state = null;

          // Exit recording mode
          ref.read(chatRecordingModeProvider.notifier).state = false;
        }
      });
    });

    // Listen for tab changes to scroll to bottom when chat tab is selected
    // and refresh suggested prompts if chat is empty
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listenManual(ideabookDetailTabProvider, (previous, current) {
        // If the current tab is the chat tab (index 1)
        if (current == 1) {
          Logger.debug('Chat tab selected, scrolling to bottom immediately');

          // Directly position the view at the bottom without any animation or delay
          // This ensures we're exactly at the bottom with no bouncy effect
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (_scrollController.hasClients) {
              // Force an immediate jump to the exact bottom without animation or delay
              _scrollController.jumpTo(
                _scrollController.position.maxScrollExtent,
              );
            }
          });

          // Check if chat is empty and refresh suggested prompts
          final chatState = ref.read(chatNotifierProvider(widget.ideabook.id));
          final promptsState = ref.read(
            suggestedPromptsProvider(widget.ideabook.id),
          );

          if (chatState.messages.isEmpty && !promptsState.isLoading) {
            Logger.debug(
              'Chat is empty and prompts not loading, refreshing suggested prompts',
            );
            ref
                .read(suggestedPromptsProvider(widget.ideabook.id).notifier)
                .refresh();
          } else if (chatState.messages.isEmpty && promptsState.isLoading) {
            Logger.debug('Chat is empty but prompts are already being loaded');
          }
        }
      });
    });
  }

  // Handle text changes
  void _onTextChanged() {
    final newText = _textController.text;
    final newIsEmpty = newText.trim().isEmpty;
    final newIsOverLimit = newText.length > _characterLimit;

    if (_isTextEmpty != newIsEmpty || _isOverCharLimit != newIsOverLimit) {
      setState(() {
        _isTextEmpty = newIsEmpty;
        _isOverCharLimit = newIsOverLimit;
      });
    }
  }

  /// Initialize the maximum notes per ideabook limit from the provider
  Future<void> _initializeMaxNotesPerIdeabook() async {
    try {
      _maxNotesPerIdeabook = await ref.read(maxNotesPerIdeabookProvider.future);
      Logger.debug(
        'ChatTab: Max notes per ideabook initialized: $_maxNotesPerIdeabook',
      );
    } catch (e) {
      Logger.error(
        'ChatTab: Failed to get max notes per ideabook, using fallback',
        e,
      );
      _maxNotesPerIdeabook = 10; // Fallback to free tier limit
    }
  }

  @override
  void dispose() {
    _textController.removeListener(_onTextChanged);
    _textController.dispose();
    _scrollController.dispose();
    _inputScrollController.dispose();
    _focusNode.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Watch the ideas for this ideabook
    final ideasAsync = ref.watch(ideabookIdeasProvider(widget.ideabook.id));

    // Watch the chat state
    final chatState = ref.watch(chatNotifierProvider(widget.ideabook.id));

    // Check if rate limit is reached
    final rateLimitResult = ref.watch(chatRateLimitStatusProvider);
    final isRateLimited =
        rateLimitResult != null && rateLimitResult.isLimitReached;

    // Only scroll to bottom when new messages are added (not when existing messages are updated)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Check if the number of messages has increased (new message added)
      if (chatState.messages.length > _previousMessageCount) {
        // Immediately jump to the bottom without any animation or delay
        if (_scrollController.hasClients) {
          // Force immediate jump to the exact bottom
          _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
        }
      }
      // Update the previous count for next comparison
      _previousMessageCount = chatState.messages.length;
    });

    return Column(
      children: [
        // Error message if any (but never for rate limit errors)
        if (chatState.errorMessage != null &&
            !chatState.errorMessage!.toLowerCase().contains('rate limit') &&
            !chatState.errorMessage!.contains('recharge') &&
            !isRateLimited)
          Container(
            padding: const EdgeInsets.all(8.0),
            color: NoejiTheme.colorsOf(context).error,
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    // Sanitize the error message
                    ErrorUtils.sanitizeErrorMessage(chatState.errorMessage!),
                    style: NoejiTheme.textStylesOf(
                      context,
                    ).bodySmall.copyWith(color: Colors.white),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () {
                    ref
                        .read(chatNotifierProvider(widget.ideabook.id).notifier)
                        .clearError();
                  },
                ),
              ],
            ),
          ),

        // Chat messages
        Expanded(
          child:
              chatState.messages.isEmpty
                  ? Stack(
                    children: [
                      // Empty state message - always centered in the available space
                      Positioned.fill(
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.chat_bubble_outline,
                                size: 64,
                                color:
                                    NoejiTheme.colorsOf(context).textSecondary,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'Chat with your ideabook',
                                style:
                                    NoejiTheme.textStylesOf(context).bodyLarge,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Ask questions about your ideas or get suggestions',
                                style:
                                    NoejiTheme.textStylesOf(context).bodySmall,
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Suggested prompts positioned at the bottom
                      Positioned(
                        left: 0,
                        right: 0,
                        bottom: 16.0,
                        child: SuggestedPrompts(
                          ideabookId: widget.ideabook.id,
                          onPromptSelected: (prompt) {
                            // Set the text in the text field and send it
                            _textController.text = prompt;
                            // Use the current state of ideasAsync
                            _sendMessage(ideasAsync);
                          },
                        ),
                      ),
                    ],
                  )
                  : ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.only(
                      top: 16.0,
                      right: 16.0,
                      bottom: 16.0,
                      left: 16.0,
                    ),
                    itemCount:
                        chatState.messages.length +
                        (chatState.isLoading ? 1 : 0),
                    itemBuilder: (context, index) {
                      // If we're at the last item and loading, show the thinking indicator
                      if (chatState.isLoading &&
                          index == chatState.messages.length) {
                        return ThinkingIndicator(
                          ideabookId: widget.ideabook.id,
                        );
                      }

                      // Otherwise show the regular message
                      final message = chatState.messages[index];
                      return ChatMessageBubble(
                        message: message,
                        isUser: message.role == MessageRole.user,
                        ideabookId: widget.ideabook.id,
                      );
                    },
                  ),
        ),

        // We don't need a separate loading indicator anymore as we show the thinking message in the chat

        // Chat input - no padding for minimalistic look
        _buildInputSection(context, chatState, ideasAsync),
      ],
    );
  }

  /// Build the input section based on the current state
  Widget _buildInputSection(
    BuildContext context,
    ChatState chatState,
    AsyncValue<List<Idea>> ideasAsync,
  ) {
    // Check if we're in recording mode
    final isRecording = ref.watch(chatRecordingModeProvider);

    // Check if rate limit is reached
    final rateLimitResult = ref.watch(chatRateLimitStatusProvider);
    final isRateLimited =
        rateLimitResult != null && rateLimitResult.isLimitReached;

    if (isRecording) {
      // Show recording panel
      return ReusableAudioRecordingPanel(
        controller: ref.watch(chatRecordingControllerProvider),
        config: AudioRecordingPanelConfig(
          layout: AudioRecordingPanelLayout.horizontal,
          showTimer: false,
          color: NoejiTheme.colorsOf(context).textPrimary,
        ),
      );
    } else {
      // Regular chat input
      return Container(
        decoration: BoxDecoration(
          color: NoejiTheme.colorsOf(context).cardBackground,
          border: Border(
            top: BorderSide(
              color: NoejiTheme.colorsOf(context).border,
              width: 1,
            ),
          ),
        ),
        // No fixed height - allow it to expand with the text field
        constraints: const BoxConstraints(
          minHeight: 50, // Minimum height to ensure it's not too small
        ),
        child: Column(
          children: [
            // Rate limit message when limit is reached
            if (isRateLimited && rateLimitResult.message != null)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                color: NoejiTheme.colorsOf(context).error.withAlpha(25),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        rateLimitResult.message!,
                        style: NoejiTheme.textStylesOf(
                          context,
                        ).bodySmall.copyWith(
                          color: NoejiTheme.colorsOf(context).error,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    // Timer icon removed as per requirements
                  ],
                ),
              ),
            // Character limit indicator when over limit - only show if not rate limited
            if (!isRateLimited && _isOverCharLimit)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 4,
                ),
                color: NoejiTheme.colorsOf(context).error.withAlpha(25),
                child: Text(
                  'Message exceeds $_characterLimit character limit',
                  style: NoejiTheme.textStylesOf(context).bodySmall.copyWith(
                    color: NoejiTheme.colorsOf(context).error,
                  ),
                ),
              ),
            // Only show the input row if not rate limited
            if (!isRateLimited)
              Row(
                children: [
                  // Text input with send button inside
                  Expanded(
                    child: Container(
                      // Calculate max height based on line height (approximately)
                      // This will limit the visible area to about 5 lines of text
                      constraints: const BoxConstraints(
                        maxHeight:
                            120, // Approximate height for 5 lines of text
                      ),
                      child: TextField(
                        controller: _textController,
                        focusNode:
                            _focusNode, // Use the focus node with key event handler
                        scrollController:
                            _inputScrollController, // Use scroll controller for scrolling
                        decoration: InputDecoration(
                          hintText: 'Chat with your ideabook...',
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 14,
                          ),
                          hintStyle: NoejiTheme.textStylesOf(
                            context,
                          ).bodyMedium.copyWith(
                            color: NoejiTheme.colorsOf(context).textSecondary,
                          ),
                          // Only show send button when text is not empty, not over limit
                          suffixIcon:
                              !_isTextEmpty &&
                                      !chatState.isLoading &&
                                      !_isOverCharLimit
                                  ? IconButton(
                                    icon: Icon(
                                      Icons.send,
                                      color:
                                          NoejiTheme.colorsOf(
                                            context,
                                          ).textPrimary,
                                      size: 18,
                                    ),
                                    onPressed: () => _sendMessage(ideasAsync),
                                    splashRadius: 20,
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                  )
                                  : _isOverCharLimit
                                  ? Icon(
                                    Icons.error_outline,
                                    color: NoejiTheme.colorsOf(context).error,
                                    size: 18,
                                  )
                                  : null,
                        ),
                        style: NoejiTheme.textStylesOf(context).bodyMedium,
                        maxLines: null, // Allow unlimited lines
                        minLines: 1, // Start with one line
                        keyboardType:
                            TextInputType.multiline, // Enable multiline input
                        textAlignVertical: TextAlignVertical.center,
                        // No onSubmitted handler - we use the focus node's onKeyEvent instead
                      ),
                    ),
                  ),

                  // Mic button - more compact
                  IconButton(
                    icon: Icon(
                      Icons.mic,
                      color: NoejiTheme.colorsOf(context).textPrimary,
                      size: 18,
                    ),
                    onPressed: chatState.isLoading ? null : _startRecording,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    constraints: const BoxConstraints(),
                    splashRadius: 20,
                  ),
                ],
              ),
          ],
        ),
      );
    }
  }

  /// Start recording audio for chat input
  Future<void> _startRecording() async {
    Logger.debug('Starting chat recording');

    // Check if microphone permission is granted
    final recordingService = ref.read(audioRecordingServiceProvider);
    final hasPermission = await recordingService.checkPermission();

    if (hasPermission) {
      // Permission already granted, enter recording mode
      ref.read(chatRecordingModeProvider.notifier).state = true;
      return;
    }

    // Request permission
    final status = await recordingService.requestPermission();
    Logger.debug('Permission request result: ${status.name}');

    if (status.isGranted) {
      // Permission granted, enter recording mode
      ref.read(chatRecordingModeProvider.notifier).state = true;
      return;
    }

    // If permission is denied, show settings dialog
    if (mounted) {
      showDialog(
        context: context,
        builder:
            (dialogContext) => AlertDialog(
              title: const Text('Microphone Permission Required'),
              content: const Text(
                'Microphone permission is required to record audio. '
                'Please enable it in app settings.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    openAppSettings();
                  },
                  child: const Text('Open Settings'),
                ),
              ],
            ),
      );
    }
  }

  /// Check if the user has reached any rate limits
  /// Returns true if the rate limit has been reached
  Future<bool> _checkRateLimit() async {
    try {
      Logger.debug('===== CHAT TAB: CHECKING RATE LIMITS =====');

      // Refresh the rate limit status
      final result = await ref.read(checkChatRateLimitProvider.future);

      // Log the result
      if (result.isLimitReached) {
        Logger.debug('Rate limit reached: ${result.message}');
        Logger.debug(
          'Next available time: ${result.nextAvailableTime?.toIso8601String()}',
        );
        Logger.debug('Limit description: ${result.limitDescription}');
        Logger.debug('Seconds remaining: ${result.secondsRemaining}');

        // Don't set error message to avoid showing top red notification
        // The bottom notification will still be shown via the rate limit status provider

        Logger.debug('=========================================');
        return true;
      } else {
        Logger.debug('No rate limit reached');
        Logger.debug('=========================================');
        return false;
      }
    } catch (e) {
      Logger.error('Error checking rate limit', e);
      return false; // Don't block the user if there's an error checking
    }
  }

  /// Handle rate limit with paywall logic
  Future<void> _handleRateLimit(dynamic rateLimitResult) async {
    try {
      // Create a ChatLimitException
      final exception = ChatLimitException(
        rateLimitResult.message ?? 'Chat rate limit reached',
      );

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Handle the limit exception with paywall logic
      final shouldContinue = await PaywallHandler.handleLimitException(
        context: context,
        ref: ref,
        exception: exception,
      );

      // If user upgraded, refresh the rate limit status
      if (shouldContinue) {
        // Trigger a fresh rate limit check
        ref.read(checkChatRateLimitProvider.future);
      }
    } catch (e) {
      Logger.error('Error handling rate limit with paywall', e);
    }
  }

  /// Check rate limit and handle with paywall logic
  /// Returns true if user can proceed, false if they should not
  Future<bool> _checkRateLimitAndHandlePaywall() async {
    try {
      // Check rate limit
      final rateLimitService = ref.read(chatRateLimitServiceProvider);
      final result = await rateLimitService.checkRateLimit();

      if (result.isLimitReached) {
        // Create a ChatLimitException
        final exception = ChatLimitException(
          result.message ?? 'Chat rate limit reached',
        );

        // Check if widget is still mounted before using context
        if (!mounted) return false;

        // Handle the limit exception with paywall logic
        final shouldContinue = await PaywallHandler.handleLimitException(
          context: context,
          ref: ref,
          exception: exception,
        );

        return shouldContinue;
      }

      return true; // No limit reached, can proceed
    } catch (e) {
      Logger.error('Error checking rate limit with paywall', e);
      return false; // Don't allow if there's an error
    }
  }

  /// Helper method to log a message for rate limiting
  Future<void> _logMessageForRateLimit() async {
    Logger.debug('===== CHAT TAB: LOGGING MESSAGE FOR RATE LIMITING =====');
    try {
      // Wait for the message to be logged before allowing another message
      final success = await ref.read(chatRateLimitServiceProvider).logMessage();
      Logger.debug('Message logged successfully: $success');

      // Check rate limits after sending a message
      Logger.debug('Checking rate limits after sending message');
      final rateLimitReached = await _checkRateLimit();

      // If rate limit was reached after sending this message, immediately show paywall
      if (rateLimitReached) {
        Logger.debug(
          'Rate limit reached after sending message, showing paywall immediately',
        );
        final rateLimitResult = ref.read(chatRateLimitStatusProvider);
        if (rateLimitResult != null && rateLimitResult.isLimitReached) {
          // Show paywall immediately after hitting the rate limit
          await _handleRateLimit(rateLimitResult);
        }
      }

      Logger.debug('======================================================');
    } catch (e) {
      Logger.error('Error logging message for rate limiting', e);
    }
  }

  Future<void> _sendMessage(AsyncValue<List<Idea>> ideasAsync) async {
    final message = _textController.text.trim();
    if (message.isEmpty) return;

    // Check if message exceeds character limit
    if (message.length > _characterLimit) {
      // Show error message via chat state
      ref
          .read(chatNotifierProvider(widget.ideabook.id).notifier)
          .setError(
            'Message exceeds $_characterLimit character limit. Please shorten your message.',
          );
      return;
    }

    // Check rate limit before sending - this does a fresh check from storage
    Logger.debug('===== CHAT TAB: CHECKING RATE LIMIT BEFORE SENDING =====');

    // Perform a fresh rate limit check and handle with paywall logic
    final isLimitReached = await _checkRateLimitAndHandlePaywall();

    if (!isLimitReached) {
      Logger.debug('Rate limit reached, aborting send');
      Logger.debug('===================================================');
      return;
    }

    Logger.debug('No rate limit reached, proceeding with sending message');
    Logger.debug('===================================================');

    // Clear the text field
    _textController.clear();

    Logger.debug('======= CHAT TAB DEBUGGING =======');
    Logger.debug('Sending message: "$message"');
    Logger.debug(
      'Ideabook ID: ${widget.ideabook.id}, Name: ${widget.ideabook.name}',
    );
    Logger.debug('Ideas async state: ${ideasAsync.runtimeType}');

    // Process ideas data based on the async state
    ideasAsync.when(
      data: (ideas) async {
        Logger.debug('Ideas async state is DATA with ${ideas.length} ideas');

        // Log the ideas for debugging
        if (ideas.isEmpty) {
          Logger.debug(
            'WARNING: Ideas list from provider is EMPTY for ideabook ${widget.ideabook.id}',
          );
          Logger.debug(
            'Falling back to direct fetching since provider returned empty list',
          );
          await _fetchIdeasAndSendMessage(message);
        } else {
          // Log each idea for debugging
          Logger.debug('Ideas from provider:');
          for (int i = 0; i < ideas.length; i++) {
            final idea = ideas[i];
            final contentPreview =
                idea.content.length > 30
                    ? '${idea.content.substring(0, 30)}...'
                    : idea.content;
            Logger.debug(
              '  Idea $i - ID: ${idea.id}, Content: "$contentPreview"',
            );
          }

          // CRITICAL CHECK: Verify ideas list is not empty
          if (ideas.isEmpty) {
            Logger.debug(
              'CRITICAL ERROR: Ideas list is empty even though provider reported non-empty list!',
            );
            Logger.debug(
              'Falling back to direct fetching since provider returned empty list',
            );
            await _fetchIdeasAndSendMessage(message);
          } else {
            // No need to verify ideas belong to the correct ideabook anymore
            // Since ideabookId is now implicit from the collection structure
            // All ideas retrieved from the ideabook's subcollection already belong to that ideabook

            // Send the message with the ideas
            Logger.debug(
              'Sending message with ${ideas.length} ideas from provider',
            );
            ref
                .read(chatNotifierProvider(widget.ideabook.id).notifier)
                .sendMessage(
                  message,
                  ideas: ideas,
                  ideabookName: widget.ideabook.name,
                );

            // Log the message for rate limiting (only once)
            await _logMessageForRateLimit();

            // Scroll to bottom after sending a message
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (_scrollController.hasClients) {
                // Force immediate jump to the exact bottom without animation or delay
                _scrollController.jumpTo(
                  _scrollController.position.maxScrollExtent,
                );
              }
            });
          }
        }
      },
      loading: () async {
        // If ideas are still loading, fetch them directly from the repository
        Logger.debug(
          'Ideas async state is LOADING, fetching directly from repository',
        );
        await _fetchIdeasAndSendMessage(message);
      },
      error: (error, stackTrace) async {
        // If there was an error loading ideas, log it and fetch directly
        Logger.debug('Ideas async state is ERROR: $error');
        Logger.debug('Stack trace: $stackTrace');
        await _fetchIdeasAndSendMessage(message);
      },
    );
    Logger.debug('======= END CHAT TAB DEBUGGING =======');
  }

  /// Show confirmation dialog before clearing chat history
  Future<void> _showClearChatConfirmationDialog() async {
    // Show confirmation dialog
    final bool? confirmClear = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            'Clear Chat',
            style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
          ),
          content: Text(
            'Are you sure you want to clear all chat history?',
            style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.zero,
            side: BorderSide(
              color: NoejiTheme.colorsOf(dialogContext).border,
              width: 1,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: Text(
                'Cancel',
                style: NoejiTheme.textStylesOf(dialogContext).buttonText,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              child: Text(
                'Clear',
                style: NoejiTheme.textStylesOf(dialogContext).buttonText,
              ),
            ),
          ],
        );
      },
    );

    // If user confirmed, clear the chat history
    if (confirmClear == true) {
      await _clearChatHistory();
    }
  }

  /// Clear chat history and reset scroll position
  Future<void> _clearChatHistory() async {
    try {
      // Clear the chat history
      await ref
          .read(chatNotifierProvider(widget.ideabook.id).notifier)
          .clearChatHistory();

      // Reset scroll position when chat is cleared
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.jumpTo(0);
        }
      });

      // Refresh suggested prompts after clearing chat
      Logger.debug('Refreshing suggested prompts after clearing chat');
      ref.read(suggestedPromptsProvider(widget.ideabook.id).notifier).refresh();
    } catch (e) {
      // Log error but don't show notification
      Logger.error('Error clearing chat history', e);
    }
  }

  /// Fetch ideas directly from the repository and send the message
  Future<void> _fetchIdeasAndSendMessage(String message) async {
    Logger.debug('======= DIRECT FETCH DEBUGGING =======');
    Logger.debug('Directly fetching ideas for ideabook ${widget.ideabook.id}');

    try {
      List<Idea> ideas = [];

      // Fetch ideas from Firestore
      try {
        Logger.debug('Fetching ideas from Firestore');
        final firestoreService = ref.read(firestoreServiceProvider);

        // Check if the ideabook exists in Firestore using listener
        final ideabook =
            await firestoreService
                .listenToIdeabookById(widget.ideabook.id)
                .first;
        if (ideabook == null) {
          Logger.debug(
            'WARNING: Ideabook ${widget.ideabook.id} not found in Firestore!',
          );
        } else {
          Logger.debug('Ideabook found in Firestore: ${ideabook.name}');
        }

        // Fetch ideas from Firestore using listener
        ideas = await firestoreService.listenToIdeas(widget.ideabook.id).first;
        Logger.debug('Fetched ${ideas.length} ideas from Firestore');

        // Log each idea from Firestore
        if (ideas.isNotEmpty) {
          Logger.debug('Ideas from Firestore:');
          for (int i = 0; i < ideas.length; i++) {
            final idea = ideas[i];
            final contentPreview =
                idea.content.length > 30
                    ? '${idea.content.substring(0, 30)}...'
                    : idea.content;
            Logger.debug(
              '  Idea $i - ID: ${idea.id}, Content: "$contentPreview"',
            );
          }
        } else {
          Logger.debug(
            'WARNING: No ideas found in Firestore for ideabook ${widget.ideabook.id}',
          );
        }
      } catch (firestoreError) {
        Logger.debug('ERROR fetching from Firestore: $firestoreError');
        // No fallback to local database anymore
      }

      // Final check and send
      if (ideas.isEmpty) {
        Logger.debug(
          'CRITICAL WARNING: Could not fetch any ideas from any source!',
        );
        Logger.debug('Sending message with empty ideas list as last resort');
      } else {
        // No need to verify ideas belong to the correct ideabook anymore
        // Since ideabookId is now implicit from the collection structure
        // All ideas retrieved from the ideabook's subcollection already belong to that ideabook

        Logger.debug(
          'Successfully fetched ${ideas.length} verified ideas, sending message',
        );
      }

      // Send the message with whatever ideas we have (might be empty)
      ref
          .read(chatNotifierProvider(widget.ideabook.id).notifier)
          .sendMessage(
            message,
            ideas: ideas,
            ideabookName: widget.ideabook.name,
          );

      // Log the message for rate limiting (only once)
      await _logMessageForRateLimit();

      // Scroll to bottom after sending a message
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          // Force immediate jump to the exact bottom without animation or delay
          _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
        }
      });
    } catch (e) {
      Logger.debug('CRITICAL ERROR in direct fetch: $e');

      // If all else fails, send with empty ideas list but log the error
      Logger.debug(
        'Sending message with empty ideas list due to critical error',
      );
      ref
          .read(chatNotifierProvider(widget.ideabook.id).notifier)
          .sendMessage(message, ideas: [], ideabookName: widget.ideabook.name);

      // Log the message for rate limiting (only once)
      await _logMessageForRateLimit();

      // Scroll to bottom after sending a message
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          // Force immediate jump to the exact bottom without animation or delay
          _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
        }
      });
    }

    Logger.debug('======= END DIRECT FETCH DEBUGGING =======');
  }
}

/// Widget for displaying suggested prompts in the chat tab
class SuggestedPrompts extends ConsumerStatefulWidget {
  /// Callback when a prompt is selected
  final Function(String) onPromptSelected;

  /// The ideabook ID
  final String ideabookId;

  /// Constructor
  const SuggestedPrompts({
    super.key,
    required this.onPromptSelected,
    required this.ideabookId,
  });

  @override
  ConsumerState<SuggestedPrompts> createState() => _SuggestedPromptsState();
}

class _SuggestedPromptsState extends ConsumerState<SuggestedPrompts>
    with SingleTickerProviderStateMixin {
  /// Default prompts to show while loading
  final List<String> _defaultPrompts = [
    "Summarize the ideabook",
    "What are the key points?",
    "What are the action items?",
    "Brainstorm more ideas",
  ];

  // Animation controller for the loading dots
  late AnimationController _dotsController;
  int _dotCount = 1;

  @override
  void initState() {
    super.initState();

    // Create animation controller for the loading dots
    _dotsController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          // Cycle through 1, 2, 3 dots
          _dotCount = (_dotCount % 3) + 1;
        });
        _dotsController.reset();
        _dotsController.forward();
      }
    });

    // Start the animation
    _dotsController.forward();

    // Ensure we have the latest suggested prompts
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final promptsState = ref.read(
        suggestedPromptsProvider(widget.ideabookId),
      );
      if (promptsState.prompts.isEmpty && !promptsState.isLoading) {
        Logger.debug(
          'No suggested prompts available and not loading, refreshing',
        );
        ref
            .read(suggestedPromptsProvider(widget.ideabookId).notifier)
            .refresh();
      } else if (promptsState.isLoading) {
        Logger.debug('Suggested prompts are already being loaded');
      } else {
        Logger.debug(
          'Suggested prompts already available: ${promptsState.prompts.length} prompts',
        );
      }
    });
  }

  @override
  void dispose() {
    _dotsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Watch the suggested prompts state
    final promptsState = ref.watch(suggestedPromptsProvider(widget.ideabookId));

    // Determine which prompts to show
    List<String> promptsToShow;
    bool isLoading = false;

    if (promptsState.isLoading) {
      // When loading, don't show any prompts yet
      promptsToShow = [];
      isLoading = true;
    } else if (promptsState.prompts.isEmpty) {
      // If generation failed or returned empty, show default prompts
      promptsToShow = _defaultPrompts;
    } else {
      // Show the generated prompts
      promptsToShow = promptsState.prompts;
    }

    // Build the dots string for the loading indicator
    final dots = '.' * _dotCount;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Show loading indicator if loading
        if (isLoading)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Fixed text part
                Text(
                  'Getting suggestions',
                  style: NoejiTheme.textStylesOf(context).bodySmall.copyWith(
                    color: NoejiTheme.colorsOf(context).textSecondary,
                  ),
                ),
                // Animated dots in a fixed-width container
                SizedBox(
                  width: 18, // Fixed width to accommodate up to 3 dots
                  child: Text(
                    dots,
                    style: NoejiTheme.textStylesOf(context).bodySmall.copyWith(
                      color: NoejiTheme.colorsOf(context).textSecondary,
                    ),
                  ),
                ),
              ],
            ),
          ),

        // Show the prompts only if we have any
        if (promptsToShow.isNotEmpty)
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children:
                    promptsToShow.map((prompt) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4.0),
                        child: InkWell(
                          onTap: () => widget.onPromptSelected(prompt),
                          child: Container(
                            width:
                                MediaQuery.of(context).size.width * 0.55 -
                                24.0, // 55% of width minus padding
                            // Calculate a fixed height that fits two lines of text
                            // This ensures consistent height even for short prompts
                            height:
                                NoejiTheme.textStylesOf(
                                      context,
                                    ).bodyMedium.fontSize! *
                                    2.5 +
                                24.0, // 2 lines + padding
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12.0,
                              vertical: 8.0,
                            ),
                            decoration: BoxDecoration(
                              color:
                                  NoejiTheme.colorsOf(
                                    context,
                                  ).chatMessageBackground,
                              // No border to match user message style
                              border: null,
                            ),
                            // Center the text vertically within the fixed height container
                            child: Center(
                              child: Text(
                                prompt,
                                style:
                                    NoejiTheme.textStylesOf(context).bodyMedium,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.left,
                              ),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
              ),
            ),
          ),
      ],
    );
  }
}

/// Widget for displaying the "Thinking..." indicator with animated dots
class ThinkingIndicator extends StatefulWidget {
  /// The ideabook ID
  final String ideabookId;

  /// Constructor
  const ThinkingIndicator({super.key, required this.ideabookId});

  @override
  State<ThinkingIndicator> createState() => _ThinkingIndicatorState();
}

class _ThinkingIndicatorState extends State<ThinkingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  int _dotCount = 1;

  @override
  void initState() {
    super.initState();

    // Create animation controller
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          // Cycle through 1, 2, 3 dots
          _dotCount = (_dotCount % 3) + 1;
        });
        _controller.reset();
        _controller.forward();
      }
    });

    // Start the animation
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Build the dots string
    final dots = '.' * _dotCount;

    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4.0),
        padding: const EdgeInsets.only(
          top: 12.0,
          right: 12.0,
          bottom: 12.0,
          left: 0.0,
        ),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width,
        ),
        decoration: const BoxDecoration(
          color: Colors.transparent,
          border: null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Fixed text part
            Text(
              'Thinking',
              style: NoejiTheme.textStylesOf(context).bodyMedium,
            ),
            // Animated dots in a fixed-width container
            SizedBox(
              width: 18, // Fixed width to accommodate up to 3 dots
              child: Text(
                dots,
                style: NoejiTheme.textStylesOf(context).bodyMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget for displaying a chat message bubble
class ChatMessageBubble extends ConsumerWidget {
  /// The message to display
  final ChatMessage message;

  /// Whether this is a user message
  final bool isUser;

  /// The ideabook ID
  final String ideabookId;

  /// Constructor
  const ChatMessageBubble({
    super.key,
    required this.message,
    required this.isUser,
    required this.ideabookId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the chat state to get the latest version of this message
    final chatState = ref.watch(chatNotifierProvider(ideabookId));

    // Find the current version of this message in the chat state
    final currentMessage = chatState.messages.firstWhere(
      (m) => m.id == message.id,
      orElse: () => message, // Fallback to the original message if not found
    );

    // Check if this is the last message in the chat
    final isLastMessage =
        chatState.messages.isNotEmpty &&
        chatState.messages.last.id == message.id &&
        !chatState.isLoading;

    // Check if this is the first LLM response in the chat
    final isFirstLLMResponse =
        !isUser &&
        chatState.messages
                .where((m) => m.role == MessageRole.assistant)
                .length ==
            1 &&
        chatState.messages
                .where((m) => m.role == MessageRole.assistant)
                .toList()[0]
                .id ==
            message.id;

    return Align(
      alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Column(
        crossAxisAlignment:
            isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          // Message bubble
          Container(
            margin: const EdgeInsets.symmetric(vertical: 4.0),
            // Different padding for user vs LLM messages
            padding:
                isUser
                    ? const EdgeInsets.all(12.0)
                    : const EdgeInsets.only(
                      top: 12.0,
                      right: 12.0,
                      bottom: 12.0,
                      left: 0.0,
                    ), // No left padding for LLM responses
            constraints: BoxConstraints(
              // Full width for LLM responses, partial width for user messages
              maxWidth:
                  isUser
                      ? MediaQuery.of(context).size.width * 0.75
                      : MediaQuery.of(context).size.width,
            ),
            decoration: BoxDecoration(
              // User messages have custom background color, LLM messages are transparent
              color:
                  isUser
                      ? NoejiTheme.colorsOf(context).chatMessageBackground
                      : Colors.transparent,
              // No border for any messages
              border: null,
            ),
            child:
                isUser
                    // User messages use simple text
                    ? Text(
                      message.content,
                      style: NoejiTheme.textStylesOf(context).bodyMedium,
                    )
                    // LLM responses use markdown or fallback to plain text if markdown fails
                    : _buildMarkdownOrText(context, message.content),
          ),

          // For AI responses only
          if (!isUser)
            // Reminder text
            Padding(
              padding: const EdgeInsets.only(top: 4.0, bottom: 8.0, left: 0.0),
              child: Text(
                "AI can make mistakes. Double check the response.",
                style: NoejiTheme.textStylesOf(context).bodySmall.copyWith(
                  color: NoejiTheme.colorsOf(context).textSecondary,
                ),
              ),
            ),

          // For AI responses only
          if (!isUser)
            // Buttons row
            Padding(
              padding: const EdgeInsets.only(top: 0.0, bottom: 12.0, left: 0.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Save as Note button or Saved indicator
                  _buildSaveAsNoteButton(
                    context,
                    ref,
                    currentMessage,
                    isFirstLLMResponse,
                  ),

                  const SizedBox(width: 8),

                  // Copy button
                  OutlinedButton.icon(
                    icon: const Icon(Icons.copy, size: 16),
                    label: const Text('Copy'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      textStyle: NoejiTheme.textStylesOf(context).bodySmall,
                    ),
                    onPressed: () {
                      // Get the copy as markdown preference
                      final copyAsMarkdown = ref.read(copyAsMarkdownProvider);

                      // Copy message content to clipboard based on preference
                      final textToCopy =
                          copyAsMarkdown
                              ? message
                                  .content // Copy with markdown formatting
                              : _cleanMarkdownForClipboard(
                                message.content,
                              ); // Copy as plain text

                      Clipboard.setData(ClipboardData(text: textToCopy));

                      // Show success message
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Copied to clipboard'),
                          duration: Duration(seconds: 1),
                        ),
                      );
                    },
                  ),

                  // Only show Clear Chat button for the last LLM message
                  if (isLastMessage && !isUser) ...[
                    const SizedBox(width: 8),

                    // Clear Chat button (renamed from New Chat)
                    OutlinedButton.icon(
                      icon: const Icon(Icons.delete_sweep, size: 16),
                      label: const Text('Clear Chat'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        textStyle: NoejiTheme.textStylesOf(context).bodySmall,
                      ),
                      onPressed: () {
                        // Find the parent ChatTab widget and call its _showClearChatConfirmationDialog method
                        final chatTabState =
                            context.findAncestorStateOfType<_ChatTabState>();
                        if (chatTabState != null) {
                          chatTabState._showClearChatConfirmationDialog();
                        } else {
                          // Fallback if we can't find the parent
                          ref
                              .read(chatNotifierProvider(ideabookId).notifier)
                              .clearChatHistory();
                        }
                      },
                    ),
                  ],
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// Build markdown content with proper handling of all characters
  Widget _buildMarkdownOrText(BuildContext context, String content) {
    try {
      // First, check if the content is JSON and extract the response field if it exists
      String processedContent = content;

      // Check if the content might be JSON or wrapped in markdown code blocks
      String contentToProcess = content;

      // Check for markdown code blocks with ```json or ``` pattern
      if (contentToProcess.trim().startsWith('```')) {
        Logger.debug(
          'Content appears to be wrapped in markdown code blocks, extracting...',
        );

        // Find the end of the code block
        final endBlockIndex = contentToProcess.lastIndexOf('```');
        if (endBlockIndex > 3) {
          // Extract content between the code blocks
          final startContentIndex =
              contentToProcess.indexOf('\n', contentToProcess.indexOf('```')) +
              1;
          if (startContentIndex > 0 && startContentIndex < endBlockIndex) {
            contentToProcess =
                contentToProcess
                    .substring(startContentIndex, endBlockIndex)
                    .trim();
            Logger.debug('Extracted content from markdown code block');
          }
        }
      }

      // Enhanced JSON detection and parsing
      bool foundResponse = false;

      // First check: direct JSON format
      if (contentToProcess.trim().startsWith('{') &&
          contentToProcess.trim().endsWith('}')) {
        try {
          // Try to parse the JSON response
          final jsonResponse =
              json.decode(contentToProcess) as Map<String, dynamic>;

          // Extract the response content if it exists
          if (jsonResponse.containsKey('response')) {
            processedContent = jsonResponse['response'] as String;
            foundResponse = true;
            Logger.debug(
              'Successfully extracted response field from JSON in chat bubble',
            );
          } else {
            // Try to find response field in nested objects
            jsonResponse.forEach((key, value) {
              if (value is Map<String, dynamic> &&
                  value.containsKey('response')) {
                processedContent = value['response'] as String;
                foundResponse = true;
                Logger.debug(
                  'Successfully extracted response field from nested JSON in chat bubble',
                );
              }
            });
          }
        } catch (e) {
          // If parsing fails, try more aggressive approaches
          Logger.debug(
            'Initial JSON parsing failed: $e, trying alternative approaches',
          );
        }
      }

      // Second check: Try to find any JSON-like structure in the content
      if (!foundResponse) {
        // Look for patterns like {"response": "..."}
        final responsePattern = RegExp(
          r'[\s\S]*?"response"[\s]*?:[\s]*?"([\s\S]*?)"[\s\S]*?',
        );
        final match = responsePattern.firstMatch(contentToProcess);

        if (match != null && match.groupCount >= 1) {
          processedContent = match.group(1) ?? processedContent;
          foundResponse = true;
          Logger.debug('Extracted response using regex pattern matching');
        }
      }

      // Third check: If it still looks like JSON but we couldn't extract the response
      if (!foundResponse &&
          contentToProcess.contains('"response"') &&
          contentToProcess.contains('{') &&
          contentToProcess.contains('}')) {
        try {
          // Try to clean up the JSON string
          String cleanedJson =
              contentToProcess
                  .replaceAll(RegExp(r'[\n\r]'), ' ') // Remove newlines
                  .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
                  .trim();

          // Find the start and end of what looks like a JSON object
          int startIdx = cleanedJson.indexOf('{');
          int endIdx = cleanedJson.lastIndexOf('}') + 1;

          if (startIdx >= 0 && endIdx > startIdx) {
            cleanedJson = cleanedJson.substring(startIdx, endIdx);

            // Try to parse the cleaned JSON
            final jsonResponse =
                json.decode(cleanedJson) as Map<String, dynamic>;

            if (jsonResponse.containsKey('response')) {
              processedContent = jsonResponse['response'] as String;
              foundResponse = true;
              Logger.debug(
                'Successfully extracted response field from cleaned JSON',
              );
            }
          }
        } catch (e) {
          Logger.debug('Cleaned JSON parsing failed: $e');
        }
      }

      // If all parsing attempts failed but content still looks like JSON, show error message
      if (!foundResponse &&
              (contentToProcess.trim().startsWith('{') &&
                  contentToProcess.trim().endsWith('}')) ||
          contentToProcess.contains('"response"')) {
        processedContent =
            "Error: Could not parse the AI response. Please try again.";
        Logger.error('Failed to parse JSON response after multiple attempts');
      }

      // Use Markdown widget with selectable text for better handling of all characters
      return MarkdownBody(
        data: processedContent,
        selectable: true, // Make text selectable which helps with rendering
        styleSheet: MarkdownStyleSheet(
          p: NoejiTheme.textStylesOf(context).bodyMedium,
          h1: NoejiTheme.textStylesOf(context).titleLarge,
          h2: NoejiTheme.textStylesOf(context).titleMedium,
          h3: NoejiTheme.textStylesOf(context).titleSmall,
          code: NoejiTheme.textStylesOf(context).bodyMedium.copyWith(
            fontFamily: 'monospace',
            backgroundColor: NoejiTheme.colorsOf(context).cardBackground,
          ),
          codeblockDecoration: BoxDecoration(
            color: NoejiTheme.colorsOf(context).cardBackground,
            border: Border.all(
              color: NoejiTheme.colorsOf(context).border,
              width: 1,
            ),
          ),
        ),
      );
    } catch (e) {
      // If markdown rendering fails, fall back to plain text
      Logger.error('Error rendering markdown: $e');
      return Text(content, style: NoejiTheme.textStylesOf(context).bodyMedium);
    }
  }

  /// Build the Save as Note button with tour tooltip if needed
  Widget _buildSaveAsNoteButton(
    BuildContext context,
    WidgetRef ref,
    ChatMessage currentMessage,
    bool isFirstLLMResponse,
  ) {
    // Create the base button
    final saveButton = OutlinedButton.icon(
      icon: Icon(
        currentMessage.isSavedAsNote ? Icons.check : Icons.save_alt,
        size: 16,
      ),
      label: Text('Save as Note'),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        textStyle: NoejiTheme.textStylesOf(context).bodySmall,
        // No longer gray out the button text when saved
        // No longer gray out the button outline when saved
        side: BorderSide(color: NoejiTheme.colorsOf(context).border),
      ),
      onPressed: () => _saveAsNote(context, ref),
    );

    // If this is the first LLM response, check if we should show the tour
    if (isFirstLLMResponse) {
      // Use the TourService to show the save as note tour
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        try {
          if (context.mounted) {
            // Add a delay of 0.5 seconds before showing the tooltip
            // This gives the user time to see the LLM response first
            await Future.delayed(const Duration(milliseconds: 0));

            if (context.mounted) {
              final shown = await TourService.showSaveAsNoteTour(context);
              Logger.debug('Save as note tour shown: $shown');
            }
          }
        } catch (e) {
          Logger.error('Error showing save as note tour', e);
        }
      });

      // Wrap the button with the Showcase widget
      return TourService.createShowcaseWidget(
        context: context,
        key: ShowcaseKeys.saveAsNoteButton,
        description:
            'Love a chat? ❤️ Save it as a note! 📝 You\'ll find all your saved gems in the \'Notes\' tab 📑.\n\nBest part? You can easily reuse that awesome prompt with fresh ideas! 🔁💡',
        child: saveButton,
      );
    }

    // Otherwise just return the button
    return saveButton;
  }

  /// Save the message as a note
  void _saveAsNote(BuildContext context, WidgetRef ref) async {
    try {
      // Get the current version of this message from the chat state
      final chatState = ref.read(chatNotifierProvider(ideabookId));
      final currentMessage = chatState.messages.firstWhere(
        (m) => m.id == message.id,
        orElse: () => message, // Fallback to the original message if not found
      );

      // Process the message content to extract the response field if it's JSON
      String processedContent = currentMessage.content;

      // Check if the content might be JSON or wrapped in markdown code blocks
      String contentToProcess = currentMessage.content;

      // Check for markdown code blocks with ```json or ``` pattern
      if (contentToProcess.trim().startsWith('```')) {
        Logger.debug(
          'Content appears to be wrapped in markdown code blocks, extracting...',
        );

        // Find the end of the code block
        final endBlockIndex = contentToProcess.lastIndexOf('```');
        if (endBlockIndex > 3) {
          // Extract content between the code blocks
          final startContentIndex =
              contentToProcess.indexOf('\n', contentToProcess.indexOf('```')) +
              1;
          if (startContentIndex > 0 && startContentIndex < endBlockIndex) {
            contentToProcess =
                contentToProcess
                    .substring(startContentIndex, endBlockIndex)
                    .trim();
            Logger.debug(
              'Extracted content from markdown code block for note saving',
            );
          }
        }
      }

      // Enhanced JSON detection and parsing
      bool foundResponse = false;

      // First check: direct JSON format
      if (contentToProcess.trim().startsWith('{') &&
          contentToProcess.trim().endsWith('}')) {
        try {
          // Try to parse the JSON response
          final jsonResponse =
              json.decode(contentToProcess) as Map<String, dynamic>;

          // Extract the response content if it exists
          if (jsonResponse.containsKey('response')) {
            processedContent = jsonResponse['response'] as String;
            foundResponse = true;
            Logger.debug(
              'Successfully extracted response field from JSON for note saving',
            );
          } else {
            // Try to find response field in nested objects
            jsonResponse.forEach((key, value) {
              if (value is Map<String, dynamic> &&
                  value.containsKey('response')) {
                processedContent = value['response'] as String;
                foundResponse = true;
                Logger.debug(
                  'Successfully extracted response field from nested JSON for note saving',
                );
              }
            });
          }
        } catch (e) {
          // If parsing fails, try more aggressive approaches
          Logger.debug(
            'Initial JSON parsing failed for note saving: $e, trying alternative approaches',
          );
        }
      }

      // Second check: Try to find any JSON-like structure in the content
      if (!foundResponse) {
        // Look for patterns like {"response": "..."}
        final responsePattern = RegExp(
          r'[\s\S]*?"response"[\s]*?:[\s]*?"([\s\S]*?)"[\s\S]*?',
        );
        final match = responsePattern.firstMatch(contentToProcess);

        if (match != null && match.groupCount >= 1) {
          processedContent = match.group(1) ?? processedContent;
          foundResponse = true;
          Logger.debug(
            'Extracted response using regex pattern matching for note saving',
          );
        }
      }

      // Third check: If it still looks like JSON but we couldn't extract the response
      if (!foundResponse &&
          contentToProcess.contains('"response"') &&
          contentToProcess.contains('{') &&
          contentToProcess.contains('}')) {
        try {
          // Try to clean up the JSON string
          String cleanedJson =
              contentToProcess
                  .replaceAll(RegExp(r'[\n\r]'), ' ') // Remove newlines
                  .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
                  .trim();

          // Find the start and end of what looks like a JSON object
          int startIdx = cleanedJson.indexOf('{');
          int endIdx = cleanedJson.lastIndexOf('}') + 1;

          if (startIdx >= 0 && endIdx > startIdx) {
            cleanedJson = cleanedJson.substring(startIdx, endIdx);

            // Try to parse the cleaned JSON
            final jsonResponse =
                json.decode(cleanedJson) as Map<String, dynamic>;

            if (jsonResponse.containsKey('response')) {
              processedContent = jsonResponse['response'] as String;
              foundResponse = true;
              Logger.debug(
                'Successfully extracted response field from cleaned JSON for note saving',
              );
            }
          }
        } catch (e) {
          Logger.debug('Cleaned JSON parsing failed for note saving: $e');
        }
      }

      // If all parsing attempts failed but content still looks like JSON, show error message
      if (!foundResponse &&
              (contentToProcess.trim().startsWith('{') &&
                  contentToProcess.trim().endsWith('}')) ||
          contentToProcess.contains('"response"')) {
        Logger.error(
          'Failed to parse JSON response for note saving after multiple attempts',
        );
        // For note saving, we'll still use the original content rather than showing an error message
      }

      // Check if the message has metadata with user_prompt
      String title = "";

      // First try to get the user_prompt from metadata
      if (currentMessage.metadata != null) {
        Logger.debug(
          'Message has metadata: ${jsonEncode(currentMessage.metadata)}',
        );

        if (currentMessage.metadata!.containsKey('user_prompt')) {
          final userPrompt = currentMessage.metadata!['user_prompt'];
          Logger.debug(
            'Found user_prompt in metadata: $userPrompt (type: ${userPrompt.runtimeType})',
          );

          if (userPrompt is String) {
            title = userPrompt;
            Logger.debug(
              'Using user_prompt from metadata as note title: "$title"',
            );
          } else {
            Logger.debug('user_prompt is not a String, converting to String');
            title = userPrompt.toString();
            Logger.debug('Converted user_prompt: "$title"');
          }
        } else {
          Logger.debug('Message metadata does not contain user_prompt key');
        }
      } else {
        Logger.debug('Message has no metadata');
      }

      // If no metadata or no user_prompt in metadata, try to extract from JSON content
      if (title.isEmpty) {
        bool foundUserPrompt = false;

        // First check: direct JSON format
        if (contentToProcess.trim().startsWith('{') &&
            contentToProcess.trim().endsWith('}')) {
          try {
            // Try to parse the JSON response
            final jsonResponse =
                json.decode(contentToProcess) as Map<String, dynamic>;

            // Extract the user_prompt if it exists
            if (jsonResponse.containsKey('user_prompt')) {
              title = jsonResponse['user_prompt'] as String;
              foundUserPrompt = true;
              Logger.debug('Extracted user_prompt from JSON content: $title');
            } else {
              // Try to find user_prompt field in nested objects
              jsonResponse.forEach((key, value) {
                if (value is Map<String, dynamic> &&
                    value.containsKey('user_prompt')) {
                  title = value['user_prompt'] as String;
                  foundUserPrompt = true;
                  Logger.debug(
                    'Extracted user_prompt from nested JSON: $title',
                  );
                }
              });
            }
          } catch (e) {
            // If parsing fails, try more aggressive approaches
            Logger.debug(
              'Initial JSON parsing failed for user_prompt: $e, trying alternative approaches',
            );
          }
        }

        // Second check: Try to find any JSON-like structure with user_prompt
        if (!foundUserPrompt && contentToProcess.contains('"user_prompt"')) {
          // Look for patterns like {"user_prompt": "..."}
          final promptPattern = RegExp(
            r'[\s\S]*?"user_prompt"[\s]*?:[\s]*?"([\s\S]*?)"[\s\S]*?',
          );
          final match = promptPattern.firstMatch(contentToProcess);

          if (match != null && match.groupCount >= 1) {
            title = match.group(1) ?? title;
            foundUserPrompt = true;
            Logger.debug(
              'Extracted user_prompt using regex pattern matching: $title',
            );
          }
        }

        // Third check: If it still looks like JSON but we couldn't extract the user_prompt
        if (!foundUserPrompt &&
            contentToProcess.contains('"user_prompt"') &&
            contentToProcess.contains('{') &&
            contentToProcess.contains('}')) {
          try {
            // Try to clean up the JSON string
            String cleanedJson =
                contentToProcess
                    .replaceAll(RegExp(r'[\n\r]'), ' ') // Remove newlines
                    .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
                    .trim();

            // Find the start and end of what looks like a JSON object
            int startIdx = cleanedJson.indexOf('{');
            int endIdx = cleanedJson.lastIndexOf('}') + 1;

            if (startIdx >= 0 && endIdx > startIdx) {
              cleanedJson = cleanedJson.substring(startIdx, endIdx);

              // Try to parse the cleaned JSON
              final jsonResponse =
                  json.decode(cleanedJson) as Map<String, dynamic>;

              if (jsonResponse.containsKey('user_prompt')) {
                title = jsonResponse['user_prompt'] as String;
                foundUserPrompt = true;
                Logger.debug(
                  'Successfully extracted user_prompt from cleaned JSON: $title',
                );
              }
            }
          } catch (e) {
            Logger.debug('Cleaned JSON parsing failed for user_prompt: $e');
          }
        }
      }

      // If still no title, try to find the corresponding user message
      if (title.isEmpty) {
        Logger.debug('No user_prompt found, looking for user message');

        // Get all messages from the chat
        final messages = chatState.messages;

        // Find the index of the current message
        final currentIndex = messages.indexWhere(
          (m) => m.id == currentMessage.id,
        );

        // If found and not the first message, look for the preceding user message
        if (currentIndex > 0) {
          // Look backward to find the most recent user message
          for (int i = currentIndex - 1; i >= 0; i--) {
            if (messages[i].role == MessageRole.user) {
              title = messages[i].content;
              Logger.debug(
                'Found user message as title: ${title.substring(0, title.length.clamp(0, 50))}${title.length > 50 ? "..." : ""}',
              );
              break;
            }
          }
        }
      }

      // If still no title, use first line of response
      if (title.isEmpty) {
        Logger.debug('No user message found, using first line of response');
        final firstLine = processedContent.split('\n').first;
        title = firstLine;
        Logger.debug(
          'Using first line as title: ${title.substring(0, title.length.clamp(0, 50))}${title.length > 50 ? "..." : ""}',
        );
      }

      // Save the note with user prompt as title and LLM response as content
      Logger.debug('Saving note with title: "$title"');
      Logger.debug(
        'Title source: ${currentMessage.metadata != null && currentMessage.metadata!.containsKey('user_prompt') ? "LLM-generated user_prompt" : "fallback method"}',
      );

      // Verify the title is not empty before saving
      if (title.isEmpty) {
        Logger.debug('WARNING: Title is empty, using fallback title');
        title = "Note from chat";
      }

      await ref
          .read(ideabookNotesNotifierProvider(ideabookId).notifier)
          .createNote(
            title: title,
            content:
                processedContent, // Use the processed content instead of raw content
          );

      // Mark the message as saved
      Logger.debug('Marking message ${currentMessage.id} as saved');
      final success = await ref
          .read(chatNotifierProvider(ideabookId).notifier)
          .updateMessage(currentMessage.id, isSavedAsNote: true);

      if (!success) {
        Logger.error('Failed to mark message as saved: ${currentMessage.id}');
      }

      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Note saved successfully'),
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (e is NotesLimitException) {
        // Handle the limit exception with paywall logic
        if (context.mounted) {
          final shouldContinue = await PaywallHandler.handleLimitException(
            context: context,
            ref: ref,
            exception: e,
          );

          // If user upgraded, try saving the note again
          if (shouldContinue && context.mounted) {
            _saveAsNote(context, ref);
          }
        }
      } else {
        // Show error message for other exceptions
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error saving note: $e'),
              duration: const Duration(seconds: 2),
              backgroundColor: NoejiTheme.colorsOf(context).error,
            ),
          );
        }
      }
    }
  }

  /// Clean markdown syntax for clipboard copying
  String _cleanMarkdownForClipboard(String text) {
    String cleaned = text;

    // Remove bold markers
    cleaned = RegExp(r'\*\*(.*?)\*\*')
        .allMatches(cleaned)
        .fold(
          cleaned,
          (prev, match) => prev.replaceFirst(match[0]!, match[1]!),
        );

    // Remove italic markers
    cleaned = RegExp(r'\*(.*?)\*')
        .allMatches(cleaned)
        .fold(
          cleaned,
          (prev, match) => prev.replaceFirst(match[0]!, match[1]!),
        );

    cleaned = RegExp(r'_(.*?)_')
        .allMatches(cleaned)
        .fold(
          cleaned,
          (prev, match) => prev.replaceFirst(match[0]!, match[1]!),
        );

    // Remove headers
    cleaned = cleaned.replaceAll(RegExp(r'#{1,6}\s+'), '');

    // Remove code blocks
    cleaned = cleaned.replaceAll(RegExp(r'```.*?```', dotAll: true), '');

    // Remove inline code
    cleaned = RegExp(r'`(.*?)`')
        .allMatches(cleaned)
        .fold(
          cleaned,
          (prev, match) => prev.replaceFirst(match[0]!, match[1]!),
        );

    // Remove bullet points
    cleaned = cleaned.replaceAll(RegExp(r'^\s*[-*+]\s+', multiLine: true), '');

    // Remove numbered lists
    cleaned = cleaned.replaceAll(RegExp(r'^\s*\d+\.\s+', multiLine: true), '');

    // Remove blockquotes
    cleaned = cleaned.replaceAll(RegExp(r'^\s*>\s+', multiLine: true), '');

    // Remove horizontal rules
    cleaned = cleaned.replaceAll(
      RegExp(r'^\s*[-*_]{3,}\s*$', multiLine: true),
      '',
    );

    // Remove links but keep the text
    cleaned = RegExp(r'\[(.*?)\]\(.*?\)')
        .allMatches(cleaned)
        .fold(
          cleaned,
          (prev, match) => prev.replaceFirst(match[0]!, match[1]!),
        );

    // Remove images
    cleaned = cleaned.replaceAll(RegExp(r'!\[.*?\]\(.*?\)'), '');

    return cleaned;
  }
}
