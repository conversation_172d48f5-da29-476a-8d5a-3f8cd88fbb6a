import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/auth/auth_service.dart';
import 'package:noeji/services/firebase/firestore_listener_pool.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/utils/logger.dart';

/// Define possible states for auth processes
enum AuthProcess { idle, signingOut, signingIn }

/// Provider for the current auth process state
final authProcessProvider = StateProvider<AuthProcess>(
  (ref) => AuthProcess.idle,
);

/// Provider for the auth service
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

/// Provider for the current Firebase user
/// This provider listens to idTokenChanges() to detect authentication state changes
/// including when the app is restarted
final firebaseUserProvider = StreamProvider<firebase_auth.User?>((ref) {
  final authService = ref.watch(authServiceProvider);

  // Log the current user state when the provider is first created
  final currentUser = authService.currentUser;
  if (currentUser != null) {
    Logger.debug('firebaseUserProvider: Current user is ${currentUser.email}');
  } else {
    Logger.debug('firebaseUserProvider: No current user');
  }

  // Return the stream of auth state changes
  return authService.authStateChanges;
});

/// Provider to check if user is signed in
/// This provider depends on firebaseUserProvider and will update
/// whenever the authentication state changes
final isSignedInProvider = Provider<bool>((ref) {
  final userAsync = ref.watch(firebaseUserProvider);

  // Handle the different states of the async value
  return userAsync.when(
    // When we have data, check if the user is not null
    data: (user) {
      final isSignedIn = user != null;
      Logger.debug(
        'isSignedInProvider: User is ${isSignedIn ? 'signed in' : 'signed out'}',
      );
      return isSignedIn;
    },
    // When loading, check the current user directly as a fallback
    loading: () {
      final currentUser = firebase_auth.FirebaseAuth.instance.currentUser;
      final isSignedIn = currentUser != null;
      Logger.debug(
        'isSignedInProvider (loading): User is ${isSignedIn ? 'signed in' : 'signed out'}',
      );
      return isSignedIn;
    },
    // When there's an error, assume the user is not signed in
    error: (error, stackTrace) {
      Logger.error('isSignedInProvider: Error checking auth state', error);
      return false;
    },
  );
});

/// Provider for safe sign out with listener cleanup
/// This provider ensures all Firestore listeners are cleared BEFORE signing out
/// to prevent permission errors, and also handles RevenueCat logout
final signOutProvider = Provider<Future<void> Function()>((ref) {
  return () async {
    // Set state to signingOut
    ref.read(authProcessProvider.notifier).state = AuthProcess.signingOut;
    try {
      Logger.debug(
        'Starting safe sign out with comprehensive listener cleanup',
      );

      // Step 1: Clear all Firestore listeners from the listener pool
      final listenerPool = ref.read(firestoreListenerPoolProvider);
      Logger.debug('Clearing all Firestore listeners from listener pool');
      listenerPool.clearAllListeners();

      // Step 2: Cancel ALL Firestore subscriptions globally (including any not tracked by the pool)
      final globalRegistry = GlobalFirestoreSubscriptionRegistry();
      Logger.debug('Canceling all globally registered Firestore subscriptions');
      globalRegistry.cancelAll();

      // Step 3: Logout from RevenueCat before Firebase sign out
      final revenueCatService = ref.read(revenueCatServiceProvider);
      if (revenueCatService.isConfigured) {
        Logger.debug('Logging out from RevenueCat');
        try {
          await revenueCatService.logout();
          Logger.debug('RevenueCat logout successful');
        } catch (e) {
          Logger.error('Error during RevenueCat logout', e);
          // Continue with sign out even if RevenueCat logout fails
        }
      } else {
        Logger.debug('RevenueCat not configured, skipping RevenueCat logout');
      }

      // Step 4: Wait a brief moment to ensure listeners are fully cleared
      await Future.delayed(const Duration(milliseconds: 300));

      // Step 5: Now perform the actual Firebase sign out
      final authService = ref.read(authServiceProvider);
      await authService.signOut();

      Logger.debug('Safe sign out completed successfully');
    } catch (e) {
      Logger.error('Error during safe sign out', e);
      rethrow;
    } finally {
      // Reset state to idle AFTER sign out is complete and firebaseUserProvider has likely emitted null
      // The navigation will likely happen due to firebaseUserProvider changing
      ref.read(authProcessProvider.notifier).state = AuthProcess.idle;
    }
  };
});
